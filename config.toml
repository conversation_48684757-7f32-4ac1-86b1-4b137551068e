# 这是一个配置文件范例.
# 所有账户信息为生成, 请填写您的账户信息.
# 查看帮助与详情: https://emby-keeper.github.io#安装与使用

# ================================================================================
# Emby 保活相关设置
# 详见: https://emby-keeper.github.io/guide/配置文件#emby-子项
# ================================================================================

[emby]

# 每次进行进行 Emby 保活的当日时间范围, 可以为单个时间 ("8:00AM") 或时间范围 ("<8:00AM,10:00AM>"):
time_range = "<11:00AM,11:00PM>"

# 每隔几天进行 Emby 保活:
interval_days = "<7,12>"

# 最大可同时进行的站点数:
concurrency = 1

# ================================================================================
# Emby 账号, 您可以重复该片段多次以增加多个账号.
# 详见: https://emby-keeper.github.io/guide/配置文件#emby-account-子项
# ================================================================================

[[emby.account]] # 第 1 个账号

# 站点域名和端口:
url = "https://free.28.al:443"

# 用户名和密码:
username = "terence"
password = "1DLczB3t"
device = "iPhone"
client = "SenPlayer"
client_version = "5.7.0"
useragent = "SenPlayer/5.7.0 (iPhone; iOS 16.6.1; Scale/2.00)"
device_id = "52FAE3E3-7B00-4976-868C-67A5BA018DE1"


# 模拟观看的随机时长范围 (秒), 可以为单个数字 (120) 或时间范围 ([120, 240]):
time = [1200, 1300]

# 以下为进阶配置, 请取消注释 (删除左侧的 #) 以使用:

# 每隔几天进行保活, 默认使用全局设置 emby.interval_days:
interval_days = "<7,12>"

# 每次进行保活的当日时间范围, 默认使用全局设置 emby.time_range:
# time_range = "<11:00AM,11:00PM>"

# 无法获取视频长度时, 依然允许播放 (默认最大播放 10 分钟左右, 可能播放超出实际长度):
allow_stream = true

# 取消注释以不使用配置文件定义的代理进行连接
# use_proxy = false

# 取消注释以禁用该账户
enabled = true

# 第 2 个账号, 如需使用请将该段取消注释并修改, 也可以添加更多账号.
# [[emby.account]]
# url = "https://thomas.info:443"
# username = "denise38"
# password = "Ct@t3Ng6*2"
# time = [300, 600]

# ================================================================================
# Telegram 机器人签到相关设置
# 详见: https://emby-keeper.github.io/guide/配置文件#checkiner-子项
# ================================================================================

[checkiner]

# 每次进行进行 Telegram 签到的当日时间范围, 可以为单个时间 ("8:00AM") 或时间范围 ("<8:00AM,10:00AM>"):
time_range = "<11:00AM,11:00PM>"

# 各个站点签到将在开始后, 等待一定时间随机启动, 使各站点错开 (分钟):
random_start = 1

# 每个站点签到的最大超时时间 (秒):
timeout = 120

# 各站点最大可重试次数 (部分站点出于安全考虑有独立的设置):
retries = 4

# 最大可同时进行的站点数:
concurrency = 1

# 每隔几天进行签到:
interval_days = 1

# ================================================================================
# Telegram 账号, 您可以重复该片段多次以增加多个账号.
# 详见: https://emby-keeper.github.io/guide/配置文件#telegram-account-子项
# ================================================================================

[[telegram.account]] # 第 1 个账号

# 带国家区号的账户手机号, 一般为 "+86..."
phone = "+*************"

# 启用机器人签到系列功能, 默认启用, 设置为 false 以禁用:
checkiner = true

# 启用群组监控系列功能, 包括抢邀请码和回答问题等, 默认禁用, 设置为 true 以启用:
monitor = false

# 启用自动水群系列功能, 风险较高, 默认禁用, 设置为 true 以启用:
messager = false

# 针对该账号的独特设置, 如需使用请将该段取消注释并修改. 详见 site 项和 checkiner 项.
# [telegram.account.site]
# checkiner = ["all"]
# 
# [telegram.account.checkiner_config]
# interval_days = 1

# ================================================================================
# 站点相关设置
# 当您需要禁用某些站点时, 请将该段取消注释并修改.
# 该部分内容是根据 Embykeeper 7.3.2 生成的.
# 详见: https://emby-keeper.github.io/guide/配置文件#site-子项
# ================================================================================

# 使用 "all" 代表所有签到器, "sgk" 以代表所有社工库签到器.

# 案例 (启用所有站点, 除了社工库站点):
[site]
checkiner = ["meow", "mooncake","ikunmusic","tiannanus","terminus"]

# 默认启用站点:
# [site]
# checkiner = ["apoppro", "awatv", "cc", "dpeak_old", "dvfilm", "epub", "feiyue", "feiyuedpx", "future", "ikunmusic", "jingzhe", "jsq", "july", "kiku", "lemby", "lily", "lyrebird", "meow", "micu", "misty", "mjj", "mooncake", "moyunge", "nangua", "nebula", "pandatv", "pandatv_group", "peach", "pilipili", "plumber", "saturday", "sfcju", "shufu", "tanhua", "tdck", "tdck_new", "terminus", "tiannanus", "watermelon", "worldline", "xigua", "yezi", "yezigm", "yomo", "zm"]
# monitor = ["bgk", "future", "infinity_fly", "misty", "pornemby_answer", "pornemby_double", "pornemby_dragon_rain", "pornemby_exam", "pornemby_nohp", "viper", "woke"]
# messager = []

# 全部可用站点:
# [site]
# checkiner = ["agentsgk", "aisgk", "aivbi", "akile", "akile_group", "akuai", "alpha", "apop", "apoppro", "ask", "aurora", "awamusic", "awatv", "baidusgk", "bearsgk", "bingdaosgk", "bitsgk", "bluesea", "bostsgk", "bytevirt_group", "carll1sgk", "carll2sgk", "cc", "charon", "charon_old", "chunjiangsgk", "ciji", "datasgk", "dingdangsgk", "dogsgk", "dpeak", "dpeak_old", "dvfilm", "embyhub", "epub", "epub_group", "epub_group_chat", "fanhuasgk", "feiji", "feiyue", "feiyuedpx", "feiyuemusic", "feiyuemusic_group_old", "future", "happy", "hdhive", "heisi", "hka", "huasgk", "ikunmusic", "infsgk", "ingeeksgk", "jinding", "jingzhe", "jms", "jms_iptv", "jms_old", "johnsgk", "jsq", "judog", "july", "kiku", "koisgk", "lemby", "levilde", "lily", "ljyy", "ljyy_old", "lyrebird", "m78", "magic", "marmot_group_old", "mastersgk", "meow", "micu", "minisgk", "misty", "mjj", "mooncake", "moonkk", "moshensgk", "moyunge", "nangua", "navidrome", "nebula", "nebula_old", "niaoge", "oixel", "pandatv", "pandatv_group", "peach", "pilipili", "pingansgk", "plumber", "pornemby_game_group", "pornemby_group", "pornemby_old", "qingfengsgk", "qingmei", "raismusic", "rednosesgk", "saturday", "seedsgk", "sfcju", "shufu", "singularity", "skysink", "sssq", "starcat", "starsgk", "tanhua", "tdck", "tdck_new", "temby", "temby_beta", "temby_old", "terminus", "terminus_old", "theend", "tianmaosgk", "tiannan", "tiannanus", "unionsgk", "watermelon", "worldline", "xigua", "xraysgk", "yezi", "yezigm", "yomo", "youno", "zhihusgk", "zhipian", "zhushousgk", "zm"]
# monitor = ["bgk", "embyhub", "future", "infinity_fly", "judog", "misty", "polo", "pornemby_alert", "pornemby_answer", "pornemby_double", "pornemby_dragon_rain", "pornemby_exam", "pornemby_nohp", "pornemby_register", "shufu", "terminus_exam", "viper", "woke"]
# messager = ["marmot", "nakonako", "pornemby", "smart_pornemby"]

# ================================================================================
# 代理相关设置
# 代理设置, Emby 和 Telegram 均将通过此代理连接, 服务器位于国内时请配置代理并取消注释
# 详见: https://emby-keeper.github.io/guide/配置文件#proxy-子项
# ================================================================================

[proxy]
hostname = "127.0.0.1"
port = 7899
scheme = "http" # 可选: http / socks5

# ================================================================================
# 日志推送相关设置
# 详见: https://emby-keeper.github.io/guide/配置文件#notifier-子项
# ================================================================================

[notifier]

# 启用签到/保活结果的日志推送:
enabled = true
# 使用第几个 Telegram 账号进行推送, 从 1 开始计数:
account = 1
# 默认情况下, 日志推送将在每天指定时间统一推送 (在 @embykeeper_bot 设置), 设置为 false 以立刻推送
immediately = false
# 默认情况下, 启动时立刻执行的一次签到/保活不会推送消息, 设置为 true 以推送
once = false

# ================================================================================
# Subsonic 保活相关设置 (包括 Navidrome 和其他支持 Subsonic API 的音乐服站点)
# 详见: https://emby-keeper.github.io/guide/配置文件#subsonic-子项
# ================================================================================

[subsonic]

# 每次进行进行 Subsonic 保活的当日时间范围, 可以为单个时间 ("8:00AM") 或时间范围 ("<8:00AM,10:00AM>"):
time_range = "<11:00AM,11:00PM>"

# 每隔几天进行 Subsonic 保活:
interval_days = "<7,12>"

# 最大可同时进行的站点数:
concurrency = 1

# ================================================================================
# Subsonic 账号, 您可以重复该片段多次以增加多个账号, 如需使用, 请取消注释.
# 详见: https://emby-keeper.github.io/guide/配置文件#subsonic-account-子项
# ================================================================================

# [[subsonic.account]] # 第 1 个账号
# 
# # 站点域名和端口:
# url = "https://www.anderson.com:443"
# 
# # 用户名和密码:
# username = "hbarnes"
# password = "(4*BYMkUqw"
# 
# # 模拟观看的随机时长范围 (秒), 可以为单个数字 (120) 或时间范围 ([120, 240]):
# time = [300, 600]
# 
# [[subsonic.account]] # 第 2 个账号
# url = "https://www.abbott.com:443"
# username = "hsalazar"
# password = "Q*%D0cKW$2"
# time = [300, 600]
